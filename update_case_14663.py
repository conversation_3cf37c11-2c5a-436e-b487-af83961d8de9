#!/usr/bin/env python3
"""
Script to update case ID 14663 in tb_case table.
Removes trademark keys that start with "1_25-cv" from the images column.
"""

import sys
import os
sys.path.append(os.getcwd())

import json
import pandas as pd
from DatabaseManagement.Connections import get_gz_connection
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch


def update_case_14663():
    """
    Update case ID 14663 to remove trademark keys starting with "1_25-cv"
    """
    case_id = 14663
    
    print(f"Starting update for case ID {case_id}")
    
    # Get the specific case from database
    print("Fetching case data from database...")
    df_cases = get_table_from_GZ("tb_case", where_clause=f"id = {case_id}")
    
    if df_cases.empty:
        print(f"❌ Case ID {case_id} not found in database")
        return False
    
    if len(df_cases) > 1:
        print(f"❌ Multiple cases found with ID {case_id}")
        return False
    
    case_row = df_cases.iloc[0]
    print(f"✅ Found case: {case_row['docket']} in {case_row['court']}")
    
    # Get the images data
    images_data = case_row['images']
    if not images_data:
        print("❌ No images data found for this case")
        return False
    
    print("Current images data structure:")
    print(f"- Trademarks: {len(images_data.get('trademarks', {})) if isinstance(images_data, dict) else 'N/A'}")
    print(f"- Patents: {len(images_data.get('patents', {})) if isinstance(images_data, dict) else 'N/A'}")
    print(f"- Copyrights: {len(images_data.get('copyrights', {})) if isinstance(images_data, dict) else 'N/A'}")
    
    # Check if trademarks exist
    if not isinstance(images_data, dict) or 'trademarks' not in images_data:
        print("❌ No trademarks section found in images data")
        return False
    
    trademarks = images_data['trademarks']
    
    # Find keys that start with "1_25-cv"
    keys_to_remove = [key for key in trademarks.keys() if key.startswith('1_25-cv')]
    
    if not keys_to_remove:
        print("✅ No trademark keys starting with '1_25-cv' found. Nothing to update.")
        return True
    
    print(f"Found {len(keys_to_remove)} trademark keys to remove:")
    for key in keys_to_remove:
        print(f"  - {key}")
    
    # Create a copy of the images data and remove the keys
    updated_images_data = images_data.copy()
    updated_trademarks = updated_images_data['trademarks'].copy()
    
    for key in keys_to_remove:
        del updated_trademarks[key]
    
    updated_images_data['trademarks'] = updated_trademarks
    
    print(f"After removal:")
    print(f"- Trademarks: {len(updated_images_data['trademarks'])}")
    print(f"- Patents: {len(updated_images_data.get('patents', {}))}")
    print(f"- Copyrights: {len(updated_images_data.get('copyrights', {}))}")
    
    # Update the dataframe
    df_cases.at[0, 'images'] = updated_images_data
    
    # Save to database
    print("Updating database...")
    try:
        insert_and_update_df_to_GZ_batch(df_cases, "tb_case", "id")
        print("✅ Database update completed successfully!")
        return True
    except Exception as e:
        print(f"❌ Database update failed: {e}")
        return False


def verify_update():
    """
    Verify that the update was successful
    """
    case_id = 14663
    print(f"\nVerifying update for case ID {case_id}...")
    
    # Get the updated case from database
    df_cases = get_table_from_GZ("tb_case", where_clause=f"id = {case_id}")
    
    if df_cases.empty:
        print(f"❌ Case ID {case_id} not found in database")
        return False
    
    case_row = df_cases.iloc[0]
    images_data = case_row['images']
    
    if not isinstance(images_data, dict) or 'trademarks' not in images_data:
        print("❌ No trademarks section found in images data")
        return False
    
    trademarks = images_data['trademarks']
    
    # Check if any keys starting with "1_25-cv" still exist
    remaining_keys = [key for key in trademarks.keys() if key.startswith('1_25-cv')]
    
    if remaining_keys:
        print(f"❌ Verification failed! Still found {len(remaining_keys)} keys starting with '1_25-cv':")
        for key in remaining_keys:
            print(f"  - {key}")
        return False
    else:
        print("✅ Verification successful! No keys starting with '1_25-cv' found.")
        print(f"Current trademark count: {len(trademarks)}")
        return True


if __name__ == "__main__":
    print("=" * 60)
    print("Case 14663 Update Script")
    print("Removing trademark keys starting with '1_25-cv'")
    print("=" * 60)
    
    # Perform the update
    success = update_case_14663()
    
    # if success:
    #     # Verify the update
    #     verify_update()
    
    print("\nScript completed.")
