import unicodedata
import json
import re

def analyze_invisible_characters(text):
    """Analyze text for invisible and problematic characters"""
    if not text:
        return {"status": "empty", "issues": []}
    
    analysis = {
        "total_length": len(text),
        "invisible_chars": [],
        "control_chars": [],
        "unusual_chars": [],
        "encoding_issues": [],
        "json_issues": []
    }
    
    # Check each character
    for i, char in enumerate(text):
        char_code = ord(char)
        char_name = unicodedata.name(char, f"U+{char_code:04X}")
        char_category = unicodedata.category(char)
        
        # Detect invisible characters
        if char_code in [
            0x200B,  # Zero Width Space
            0x200C,  # Zero Width Non-Joiner  
            0x200D,  # Zero Width Joiner
            0x2060,  # Word Joiner
            0xFEFF,  # Byte Order Mark
            0x00A0,  # Non-breaking Space
            0x2028,  # Line Separator
            0x2029,  # Paragraph Separator
            0x202A,  # Left-to-Right Embedding
            0x202B,  # Right-to-Left Embedding
            0x202C,  # Pop Directional Formatting
            0x202D,  # Left-to-Right Override
            0x202E,  # Right-to-Left Override
        ]:
            analysis["invisible_chars"].append({
                "position": i,
                "char": repr(char),
                "code": f"U+{char_code:04X}",
                "name": char_name
            })
        
        # Detect control characters (except common ones)
        elif char_category.startswith('C') and char not in '\n\r\t':
            analysis["control_chars"].append({
                "position": i,
                "char": repr(char),
                "code": f"U+{char_code:04X}",
                "name": char_name,
                "category": char_category
            })
        
        # Detect unusual high-code characters
        elif char_code > 0x1000 and char_category in ['Cf', 'Co', 'Cs']:
            analysis["unusual_chars"].append({
                "position": i,
                "char": repr(char),
                "code": f"U+{char_code:04X}",
                "name": char_name,
                "category": char_category
            })
    
    # Check for JSON-specific issues if it looks like JSON
    if text.strip().startswith('{') and text.strip().endswith('}'):
        try:
            json.loads(text)
            analysis["json_status"] = "valid"
        except json.JSONDecodeError as e:
            analysis["json_status"] = "invalid"
            analysis["json_error"] = str(e)
            
            # Try to identify specific JSON issues
            if '"English"' not in text:
                analysis["json_issues"].append("Missing 'English' key")
            if '"Chinese"' not in text:
                analysis["json_issues"].append("Missing 'Chinese' key")
            if text.count('"') % 2 != 0:
                analysis["json_issues"].append("Unmatched quotes")
            if '\\' in text and not re.search(r'\\["\\/bfnrt]', text):
                analysis["json_issues"].append("Invalid escape sequences")
    
    return analysis

def print_character_analysis(text, label="Text"):
    """Print detailed analysis of text characters"""
    print(f"\n=== CHARACTER ANALYSIS FOR {label} ===")
    print(f"Length: {len(text)}")
    print(f"First 100 chars (repr): {repr(text[:100])}")
    
    analysis = analyze_invisible_characters(text)
    
    if analysis["invisible_chars"]:
        print(f"\n🚨 INVISIBLE CHARACTERS FOUND ({len(analysis['invisible_chars'])}):")
        for char_info in analysis["invisible_chars"][:5]:  # Show first 5
            print(f"  Position {char_info['position']}: {char_info['name']} ({char_info['code']})")
    
    if analysis["control_chars"]:
        print(f"\n⚠️  CONTROL CHARACTERS FOUND ({len(analysis['control_chars'])}):")
        for char_info in analysis["control_chars"][:5]:  # Show first 5
            print(f"  Position {char_info['position']}: {char_info['name']} ({char_info['code']})")
    
    if analysis["unusual_chars"]:
        print(f"\n🔍 UNUSUAL CHARACTERS FOUND ({len(analysis['unusual_chars'])}):")
        for char_info in analysis["unusual_chars"][:5]:  # Show first 5
            print(f"  Position {char_info['position']}: {char_info['name']} ({char_info['code']})")
    
    if "json_status" in analysis:
        print(f"\n📋 JSON STATUS: {analysis['json_status']}")
        if analysis["json_status"] == "invalid":
            print(f"   Error: {analysis.get('json_error', 'Unknown')}")
            if analysis["json_issues"]:
                print(f"   Issues: {', '.join(analysis['json_issues'])}")
    
    if not any([analysis["invisible_chars"], analysis["control_chars"], analysis["unusual_chars"]]):
        print("✅ No problematic characters detected")
    
    return analysis

# Test with your examples
def test_examples():
    """Test the examples you provided"""
    
    # Working example (14718)
    working_example = """{
  "English": "This document is a copyright infringement complaint filed by Alison Friend in the United States District Court for the Northern District of Illinois, Eastern Division (Case No.: 1:25-cv-04313). The plaintiff, an artist who creates humorous animal portraits, alleges that numerous unnamed partnerships and unincorporated associations (listed in an attached Schedule A) are selling unauthorized products bearing infringing versions of her copyrighted works through multiple online stores. Friend owns several U.S. Copyright Registrations (listed as \"Alison Friend Works\") that predate the alleged infringement. The defendants allegedly target U.S. consumers, including Illinois residents, through interactive commercial websites that ship to the U.S., accept payment in U.S. dollars, and have reportedly sold infringing products to Illinois residents. Friend claims irreparable damage through loss of control over her creative content, reputation, goodwill, quality, and licensing ability. She seeks both injunctive and monetary relief, noting that traditional takedown procedures would be ineffective against these defendants who conceal their identities and operate numerous interconnected stores.",
  "Chinese": "本文件是由艾莉森·弗兰德在美国伊利诺伊州北区联邦地区法院东部分院提起的版权侵权诉讼（案件编号：1:25-cv-04313）。原告是一位创作幽默动物肖像的艺术家，她声称众多未具名的合伙企业和非法人协会（列于附件A）正在通过多个在线商店销售未经授权的产品，这些产品包含侵犯其受版权保护作品的版本。弗兰德拥有多项美国版权注册（列为“艾莉森·弗兰德作品”），这些注册早于被指控的侵权行为。被告据称通过互动式商业网站针对美国消费者，包括伊利诺伊州居民，这些网站向美国发货，接受美元支付，并且据报道已向伊利诺伊州居民销售侵权产品。弗兰德声称因失去对其创意内容、声誉、商誉、质量和许可能力的控制而遭受了无法弥补的损害。她寻求禁令和金钱赔偿，并指出传统的删除程序对这些隐藏身份并运营众多相互关联商店的被告无效。"
}"""

    # Not working example (14714) 
    not_working_example = """{
  "English": "This is a legal complaint filed by Capcom Co., Ltd. in the U.S. District Court for the Northern District of Illinois (Case No. 25-cv-09096) against unnamed partnerships and unincorporated associations (identified in Schedule A) for intellectual property infringement. The nature of the case involves alleged trademark counterfeiting and copyright infringement related to Capcom's Resident Evil franchise and other properties. The defendants are e-commerce store operators who allegedly sell unauthorized products using Capcom's trademarks and copyrighted works, targeting U.S. consumers including Illinois residents through online stores operating under various seller aliases. Capcom, an established video game company founded in 1979 and creator of franchises including Resident Evil, Street Fighter, and Monster Hunter, claims it has suffered irreparable damage through consumer confusion and dilution of its valuable trademarks. The plaintiff seeks both injunctive and monetary relief to stop the defendants from continuing to sell counterfeit products and to recover damages.",
  "Chinese": "这是卡普空有限公司（Capcom Co., Ltd.）在美国伊利诺伊州北区地方法院提起的法律诉讼（案件编号：25-cv-09096），起诉未具名的合伙企业和非法人协会（详见附表A）侵犯知识产权。案件性质涉及对卡普空《生化危机》系列和其他财产的商标伪造和版权侵权指控。被告是电子商务商店的经营者，他们涉嫌使用卡普空的商标和受版权保护的作品销售未经授权的产品，通过以各种卖家别名运营的在线商店，针对包括伊利诺伊州居民在内的美国消费者。卡普空是一家成立于1979年的知名视频游戏公司，创造了包括《生化危机》、《街头霸王》和《怪物猎人》等系列游戏，声称其因消费者混淆和其有价值商标的淡化而遭受了无法弥补的损害。原告寻求禁令救济和金钱赔偿，以阻止被告继续销售假冒产品并追回损失。"
}"""

    print_character_analysis(working_example, "WORKING Example (14718)")
    print_character_analysis(not_working_example, "NOT WORKING Example (14714)")

if __name__ == "__main__":
    test_examples()