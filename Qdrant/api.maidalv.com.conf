# ------------ Backup upstream (fixed) ---------------------------------------
upstream tro_backup {
    server tro_app_api:5000;
}

# ------------ Server block --------------------------------------------------
server {
    listen 443 ssl http2;
    server_name api.maidalv.com;

    # ------------ Primary target is injected by vast-updater.sh (sets $VAST_IP and $VAST_PORT)-------------------
    include /etc/nginx/includes/vast-dynamic.conf;

    ssl_certificate     /etc/nginx/certs/maidalv.com.crt;
    ssl_certificate_key /etc/nginx/certs/maidalv.com.key;

    access_log /dev/stdout;
    error_log  /dev/stderr;

    client_max_body_size 10M;

    # ---------------- Primary → Vast ---------------------------------------
    location / {
        # primary (Vast) – value comes from the include above
        proxy_pass http://$VAST_IP:$VAST_PORT;

        proxy_set_header Host              $host;
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        add_header X-Upstream-Server $upstream_addr always;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header   Upgrade $http_upgrade;
        proxy_set_header   Connection "upgrade";

        # Timeouts
        proxy_connect_timeout 3s;
        proxy_send_timeout    10s;
        proxy_read_timeout    10s;

        # If the primary is down/slow, fall through to backup:
        proxy_next_upstream error timeout invalid_header http_502 http_503 http_504;
        proxy_next_upstream_tries 1;

        error_page 502 504 = @fallback;
    }

    # ---------------- Fallback → local Docker service -----------------------
    location @fallback {
        proxy_pass http://tro_backup;
        proxy_set_header Host              $host;
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        add_header X-Upstream-Server $upstream_addr always;
    }
}