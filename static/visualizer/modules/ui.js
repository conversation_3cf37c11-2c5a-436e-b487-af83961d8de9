import {
    casesContainer,
    totalResultsEl,
    lastRefreshEl,
    prevPageBtn,
    nextPageBtn,
    pageInfoEl,
    caseTypeDropdownHeader,
    caseTypeCheckboxes
} from './dom.js';
import { currentPage, totalPages, proposedPlaintiffNames } from './state.js';
import { updateCaseWithOptions, improvePlaintiffName, fetchCaseSteps, fetchHistoricalLogs } from './api.js';
import { addInfoItem, getImagePath, addIpInfoItem, makeInfoItemValueLinkable, createMultiSiteSearchLinks, addStepsInfoItem, createOrGetLogSection } from './helpers.js';

export function renderCases(cases) {
    if (cases.length === 0) {
        casesContainer.innerHTML = '<div class="no-results">No cases match your search criteria</div>';
        return;
    }
    casesContainer.innerHTML = '';
    cases.forEach(caseData => {
        const caseCard = renderSingleCaseCard(caseData);
        casesContainer.appendChild(caseCard);
    });
}

export function renderSingleCaseCard(caseData) {
    const caseCard = document.createElement('div');
    caseCard.className = 'card case-card';
    caseCard.dataset.caseId = caseData.id;

    const caseHeader = document.createElement('div');
    caseHeader.className = 'case-header';

    const updateButton = document.createElement('button');
    updateButton.className = 'update-case-btn';
    updateButton.innerHTML = '<i class="fas fa-sync-alt"></i>';
    updateButton.title = 'Update this case';
    updateButton.dataset.caseId = caseData.id;
    updateButton.addEventListener('click', function() {
        showUpdateCaseOverlay(caseData.id);
    });
    caseHeader.appendChild(updateButton);

    const caseIdEl = document.createElement('span');
    caseIdEl.className = 'case-id';
    caseIdEl.style.display = 'none';
    caseIdEl.textContent = caseData.id;
    caseHeader.appendChild(caseIdEl);

    const caseTitleContainer = document.createElement('div');
    caseTitleContainer.className = 'case-title-container';
    caseTitleContainer.style.display = 'flex';
    caseTitleContainer.style.alignItems = 'center';
    caseTitleContainer.style.gap = '8px';
    caseTitleContainer.style.flexGrow = '1';

    const caseTitle = document.createElement('div');
    caseTitle.className = 'case-title';
    const plaintiffName = caseData.plaintiff_name || 'Unknown Plaintiff';

    const hasProposedName = proposedPlaintiffNames &&
                              proposedPlaintiffNames[caseData.id] &&
                              proposedPlaintiffNames[caseData.id] !== plaintiffName;

    if (hasProposedName) {
        caseTitle.innerHTML = `${plaintiffName} <span class="proposed-name"><a href="/plaintiff_review">(New proposed name: ${proposedPlaintiffNames[caseData.id]})</a></span>`;
    } else {
        caseTitle.textContent = plaintiffName;
    }

    caseTitleContainer.appendChild(caseTitle);

    const improveBtn = document.createElement('button');
    improveBtn.className = 'improve-plaintiff-btn';
    improveBtn.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i>';
    improveBtn.title = 'Improve Plaintiff Name';
    improveBtn.dataset.caseId = caseData.id;
    improveBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        improvePlaintiffName(this);
    });
    caseTitleContainer.appendChild(improveBtn);

    caseHeader.appendChild(caseTitleContainer);

    const validationIcons = document.createElement('div');
    validationIcons.className = 'validation-icons';
    const validationStatus = caseData.validation_status || '';

    const validatedIcon = document.createElement('div');
    validatedIcon.className = `validation-icon ${validationStatus === 'validated' ? 'active validated' : ''}`;
    validatedIcon.innerHTML = '<i class="fas fa-check"></i>';
    validatedIcon.title = 'Validated';
    validatedIcon.dataset.status = 'validated';
    validatedIcon.dataset.caseId = caseData.id;

    const reviewIcon = document.createElement('div');
    reviewIcon.className = `validation-icon ${validationStatus === 'review_required' ? 'active review' : ''}`;
    reviewIcon.innerHTML = '<i class="far fa-lightbulb"></i>';
    reviewIcon.title = 'Review Required';
    reviewIcon.dataset.status = 'review_required';
    reviewIcon.dataset.caseId = caseData.id;

    const failedIcon = document.createElement('div');
    failedIcon.className = `validation-icon ${validationStatus === 'failed' ? 'active failed' : ''}`;
    failedIcon.innerHTML = '<i class="fas fa-times"></i>';
    failedIcon.title = 'Failed';
    failedIcon.dataset.status = 'failed';
    failedIcon.dataset.caseId = caseData.id;

    [validatedIcon, reviewIcon, failedIcon].forEach(icon => {
        icon.addEventListener('click', function() {
            const newStatus = this.dataset.status;
            const caseId = this.dataset.caseId;
            const currentStatus = caseData.validation_status || '';
            const statusToSend = currentStatus === newStatus ? '' : newStatus;
            updateValidationStatus(caseId, statusToSend);
        });
    });

    validationIcons.appendChild(validatedIcon);
    validationIcons.appendChild(reviewIcon);
    validationIcons.appendChild(failedIcon);
    caseHeader.appendChild(validationIcons);

    const caseDetailsRow = document.createElement('div');
    caseDetailsRow.className = 'case-details-row';

    const caseInfoLeft = document.createElement('div');
    caseInfoLeft.className = 'case-info-left';

    let dateFiled = caseData.date_filed || '';
    let formattedDateForPath = '';
    if (dateFiled) {
        try {
            const dateObj = new Date(dateFiled);
            if (!isNaN(dateObj.getTime())) {
                dateFiled = dateObj.toISOString().split('T')[0];
                formattedDateForPath = dateFiled;
            } else {
                dateFiled = caseData.date_filed;
            }
        } catch (e) {
            dateFiled = caseData.date_filed || 'N/A';
        }
    } else {
        dateFiled = 'N/A';
    }

    addInfoItem(caseInfoLeft, 'Case #:', `(${caseData.id}) ${caseData.docket || 'N/A'}`);
    addInfoItem(caseInfoLeft, 'Court:', caseData.court || 'N/A');
    addInfoItem(caseInfoLeft, 'Type:', caseData.nos_description || 'N/A');
    addInfoItem(caseInfoLeft, 'Status:', caseData.class_code || 'N/A');
    addInfoItem(caseInfoLeft, 'Filed:', dateFiled);
    addInfoItem(caseInfoLeft, 'Plaintiff ID:', caseData.plaintiff_id || 'N/A');
    addStepsInfoItem(caseInfoLeft, caseData.id);

    let updateTime = caseData.update_time || '';
    if (updateTime) {
        try {
            const dateObj = new Date(updateTime);
            if (!isNaN(dateObj.getTime())) {
                const year = dateObj.getFullYear();
                const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                const day = String(dateObj.getDate()).padStart(2, '0');
                const hours = String(dateObj.getHours()).padStart(2, '0');
                const minutes = String(dateObj.getMinutes()).padStart(2, '0');
                updateTime = `${year}-${month}-${day} ${hours}:${minutes}`;
            } else {
                updateTime = caseData.update_time;
            }
        } catch (e) {
            updateTime = caseData.update_time || 'N/A';
        }
    } else {
        updateTime = 'N/A';
    }
    const updatedItem = addInfoItem(caseInfoLeft, 'Updated:', updateTime);
    if (updatedItem && caseData.id) {
        updatedItem.classList.add('updated-timestamp-item');
        updatedItem.style.cursor = 'pointer';
        updatedItem.title = 'Click to view historical logs';
        updatedItem.dataset.caseId = caseData.id;
        updatedItem.addEventListener('click', function() {
            const caseId = this.dataset.caseId;
            const caseCardElement = this.closest('.case-card');
            fetchHistoricalLogs(caseId, caseCardElement);
        });
    }

    if (caseData.images_status) {
        const steps = caseData.images_status.steps_processed || [];
        addInfoItem(caseInfoLeft, 'Steps:', steps.length > 0 ? steps.join(', ') : 'None');
        addInfoItem(caseInfoLeft, 'PDFs:', caseData.images_status.number_of_pdfs || 0);
        const nextFetch = caseData.images_status.scheduled_next_fetch;
        if (nextFetch) {
            addInfoItem(caseInfoLeft, 'Next Fetch:', new Date(nextFetch).toLocaleDateString());
        }
    }

    const caseInfoRight = document.createElement('div');
    caseInfoRight.className = 'case-info-right';

    const langfuseLink = document.createElement('a');
    langfuseLink.textContent = 'Langfuse';
    langfuseLink.className = 'action-link';
    const traceUrl = caseData.images_status?.trace_url;
    if (traceUrl) {
        langfuseLink.href = traceUrl;
        langfuseLink.target = '_blank';
        langfuseLink.title = 'View on Langfuse';
    } else {
        langfuseLink.href = '#';
        langfuseLink.classList.add('disabled');
        langfuseLink.title = 'Langfuse link not available';
        langfuseLink.onclick = (e) => e.preventDefault();
    }
    caseInfoRight.appendChild(langfuseLink);

    const lnLink = document.createElement('a');
    lnLink.textContent = 'LN';
    lnLink.className = 'action-link';
    const lnUrl = caseData.ln_url;
    if (lnUrl) {
        lnLink.href = lnUrl;
        lnLink.target = '_blank';
        lnLink.title = 'View on LexisNexis';
    } else {
        lnLink.href = '#';
        lnLink.classList.add('disabled');
        lnLink.title = 'LexisNexis link not available';
        lnLink.onclick = (e) => e.preventDefault();
    }
    caseInfoRight.appendChild(lnLink);

    const fileLink = document.createElement('a');
    fileLink.textContent = 'Files';
    fileLink.className = 'action-link';
    fileLink.target = '_blank';
    const docket = caseData.docket;
    if (formattedDateForPath && docket) {
        const formattedDocket = docket.replace(/:/g, '_');
        const filePath = `/Maidalv/IP TRO Data/Case Files/${formattedDateForPath} - ${formattedDocket}/`;
        const singleEncodedPath = encodeURIComponent(filePath);
        const paramValue = `openfile=${singleEncodedPath}`;
        const fullyEncodedParam = encodeURIComponent(paramValue);
        const fileUrl = `https://synology.jslawlegal.com/index.cgi?launchApp=SYNO.SDS.App.FileStation3.Instance&launchParam=${fullyEncodedParam}`;
        fileLink.href = fileUrl;
        fileLink.title = `Open case files folder: ${filePath}`;
    } else {
        fileLink.href = '#';
        fileLink.classList.add('disabled');
        fileLink.title = 'Cannot generate file link (missing date or docket)';
        fileLink.onclick = (e) => e.preventDefault();
    }
    caseInfoRight.appendChild(fileLink);

    const dBirdLink = document.createElement('a');
    dBirdLink.textContent = 'DBird';
    dBirdLink.className = 'action-link';
    dBirdLink.target = '_blank';
    let dBirdCourtPrefix = null;
    let dBirdDocket = null;
    const court = caseData.court;
    const originalDocket = caseData.docket;
    if (court && typeof court === 'string') {
        const words = court.split(' ');
        if (words.length >= 2 && words[0].length >= 2 && words[1].length >= 1) {
            dBirdCourtPrefix = (words[0].substring(0, 2) + words[1].substring(0, 1) + 'd').toLowerCase();
        }
    }
    if (originalDocket && typeof originalDocket === 'string') {
        const match = originalDocket.match(/^([^:]*:)(\d{2})(-cv-\d+)$/);
        if (match && match.length === 4) {
            const prefix = match[1];
            const year = match[2];
            const suffix = match[3];
            dBirdDocket = `${prefix}20${year}${suffix}`;
        }
    }
    if (dBirdCourtPrefix && dBirdDocket) {
        const dBirdCaseId = `${dBirdCourtPrefix}-${dBirdDocket}`;
        const dBirdUrl = `https://www.docketbird.com/cases?case_id=${encodeURIComponent(dBirdCaseId)}`;
        dBirdLink.href = dBirdUrl;
        dBirdLink.title = `View on DocketBird: ${dBirdCaseId}`;
    } else {
        dBirdLink.href = '#';
        dBirdLink.classList.add('disabled');
        dBirdLink.title = 'Cannot generate DocketBird link (missing/invalid court or docket format)';
        dBirdLink.onclick = (e) => e.preventDefault();
    }
    caseInfoRight.appendChild(dBirdLink);

    let searchDocket = null;
    if (originalDocket && typeof originalDocket === 'string') {
        try {
            const match = originalDocket.match(/^[^:]*:(.+?-cv-)(\d+)$/);
            if (match && match.length === 3) {
                const prefix = match[1];
                const numberPart = match[2];
                const number = parseInt(numberPart, 10);
                if (!isNaN(number)) {
                    searchDocket = prefix + number.toString();
                }
            }
        } catch (e) {
            console.error(`Error processing docket for search links: ${originalDocket}`, e);
        }
    }
    createMultiSiteSearchLinks(caseInfoRight, searchDocket);

    caseDetailsRow.appendChild(caseInfoLeft);
    caseDetailsRow.appendChild(caseInfoRight);

    let caseDescription = document.createElement('div');
    caseDescription.className = 'case-description';
    let plaintiffOverview = '';
    if (caseData.plaintiff_overview) {
        try {
            if (typeof caseData.plaintiff_overview === 'string') {
                const parsed = JSON.parse(caseData.plaintiff_overview);
                plaintiffOverview = parsed.Chinese || parsed.English || '';
            } else if (caseData.plaintiff_overview.Chinese || caseData.plaintiff_overview.English) {
                plaintiffOverview = caseData.plaintiff_overview.Chinese || caseData.plaintiff_overview.English;
            }
        } catch (e) {
            plaintiffOverview = caseData.plaintiff_overview;
        }
    }
    let aiSummary = '';
    if (caseData.aisummary) {
        try {
            if (typeof caseData.aisummary === 'string') {
                const parsed = JSON.parse(caseData.aisummary);
                aiSummary = parsed.Chinese || parsed.English || '';
            } else if (caseData.aisummary.Chinese || caseData.aisummary.English) {
                aiSummary = caseData.aisummary.Chinese || caseData.aisummary.English;
            }
        } catch (e) {
            aiSummary = caseData.aisummary;
        }
    }
    if (plaintiffOverview && plaintiffOverview !== '未知') {
        caseDescription.innerHTML += `<div><strong>Plaintiff:</strong> ${plaintiffOverview}</div>`;
    }
    if (aiSummary) {
        caseDescription.innerHTML += `<div><strong>Case:</strong> ${aiSummary}</div>`;
    }

    caseCard.appendChild(caseHeader);
    caseCard.appendChild(caseDetailsRow);
    if (caseDescription.innerHTML.trim() !== '') {
        caseCard.appendChild(caseDescription);
    }

    const ipTypes = [
        { key: 'trademarks', name: 'Trademarks', statusKey: 'trademark_status' },
        { key: 'patents', name: 'Patents', statusKey: 'patent_status' },
        { key: 'copyrights', name: 'Copyrights', statusKey: 'copyright_status' }
    ];

    ipTypes.forEach(type => {
        if (caseData[type.key] && caseData[type.key].length > 0) {
            const ipSection = document.createElement('div');
            ipSection.className = 'ip-section';
            const ipTitleContainer = document.createElement('div');
            ipTitleContainer.className = 'ip-title';
            const ipTitleText = document.createElement('div');
            ipTitleText.className = 'ip-title-text';
            ipTitleText.textContent = type.name;
            ipTitleContainer.appendChild(ipTitleText);

            if (caseData.images_status && caseData.images_status[type.statusKey]) {
                const statusContainer = document.createElement('div');
                statusContainer.className = 'ip-status';
                const statusData = caseData.images_status[type.statusKey];
                const statusSources = [
                    { key: 'exhibit', label: 'Exhibit' },
                    { key: 'byregno', label: 'By Reg#' },
                    { key: 'cn_website', label: 'CN Website' },
                    { key: 'byname', label: 'By Name' },
                    { key: 'bygoogle', label: 'By Google' },
                    { key: 'manual', label: 'Manual' }
                ];
                statusSources.forEach(source => {
                    if (statusData[source.key] && statusData[source.key].count > 0) {
                        const statusItem = document.createElement('div');
                        statusItem.className = 'ip-status-item';
                        let statusText = `${source.label}: <span class="ip-status-item-count">${statusData[source.key].count}</span>`;
                        if (source.key === 'byname' && statusData[source.key].search_term_used) {
                            statusText += ` (${statusData[source.key].search_term_used})`;
                        }
                        statusItem.innerHTML = statusText;
                        statusContainer.appendChild(statusItem);
                    }
                });
                if (statusContainer.children.length > 0) {
                    ipTitleContainer.appendChild(statusContainer);
                }
            }
            ipSection.appendChild(ipTitleContainer);

            const ipItems = document.createElement('div');
            ipItems.className = 'ip-items';
            const galleryItems = (type.key !== 'patents')
                ? caseData[type.key].map(item => ({
                    src: `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${caseData.plaintiff_id}/high/${item.image}`,
                    caption: item.image
                }))
                : [];

            caseData[type.key].forEach((item, index) => {
                const ipItem = document.createElement('div');
                ipItem.className = 'ip-item';
                const highQualityItemUrl = `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${caseData.plaintiff_id}/high/${item.image}`;

                const link = document.createElement('a');
                link.href = highQualityItemUrl;
                link.style.cursor = 'pointer';
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    if (type.key === 'patents') {
                        window.Fancybox.show([{ src: highQualityItemUrl, caption: item.image }]);
                    } else {
                        window.Fancybox.show(galleryItems, { startIndex: index });
                    }
                });

                // Use high-res for patent certificate thumbnail as low-res may not exist.
                const imgUrl = (type.key === 'patents')
                    ? highQualityItemUrl
                    : getImagePath(caseData.plaintiff_id, item.image, type.key);

                const img = document.createElement('img');
                img.className = 'ip-item-image';
                img.src = imgUrl;
                img.alt = type.name;
                img.onerror = function() {
                    this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y4ZDdkYSIvPjx0ZXh0IHg9IjUwIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmaWxsPSIjNzIxYzI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+SW1hZ2UgTm90IEZvdW5kPC90ZXh0Pjx0ZXh0IHg9IjUwIiB5PSI2MCIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSI4IiBmaWxsPSIjNzIxYzI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+Q2xpY2sgdG8gc2VlIFVSTDwvdGV4dD48L3N2Zz4=';
                };
                link.appendChild(img);
                ipItem.appendChild(link);

                const ipItemInfo = document.createElement('div');
                ipItemInfo.className = 'ip-item-info';
                if (type.key === 'trademarks') {
                    const trademarkText = Array.isArray(item.trademarkText) ? item.trademarkText.join(', ') : item.trademarkText;
                    const regNo = Array.isArray(item.regNo) ? item.regNo.join(', ') : item.regNo;
                    const intClsList = Array.isArray(item.intClsList) ? item.intClsList.join(', ') : (item.intClsList || 'N/A');
                    addIpInfoItem(ipItemInfo, 'Keywords:', trademarkText || 'N/A');
                    const regNoInfoItem = addIpInfoItem(ipItemInfo, 'Reg #:', regNo || 'N/A');
                    addIpInfoItem(ipItemInfo, 'Class #:', intClsList || 'N/A');
                    makeInfoItemValueLinkable(regNoInfoItem, caseData.plaintiff_id, 'Reg #:', item.regNo, item.fullFilename);
                } else if (type.key === 'patents') {
                    addIpInfoItem(ipItemInfo, 'Name:', item.productName || 'N/A');
                    addIpInfoItem(ipItemInfo, 'Applicant:', item.applicant || 'N/A');
                    addIpInfoItem(ipItemInfo, 'Inventors:', item.inventors || 'N/A');
                    addIpInfoItem(ipItemInfo, 'Assignee:', item.assignee || 'N/A');
                    const patentInfoItem = addIpInfoItem(ipItemInfo, 'Patent #:', item.patentNumber || 'N/A');

                    // Make patent number clickable for carousel
                    if (item.grouped_images && item.grouped_images.length > 0) {
                        const valueEl = patentInfoItem.querySelector('.ip-info-value');
                        if (valueEl) {
                            valueEl.innerHTML = ''; // Clear existing text
                            const patentLink = document.createElement('a');
                            patentLink.href = '#';
                            patentLink.textContent = item.patentNumber || 'N/A';
                            patentLink.style.cursor = 'pointer';
                            patentLink.style.textDecoration = 'underline';
                            patentLink.addEventListener('click', (e) => {
                                e.preventDefault();
                                const carouselImages = item.grouped_images.map(imgName => ({
                                    src: `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${caseData.plaintiff_id}/high/${imgName}`,
                                    caption: imgName
                                }));
                                window.Fancybox.show(carouselImages);
                            });
                            valueEl.appendChild(patentLink);
                        }
                    }
                } else if (type.key === 'copyrights') {
                    const regNo = Array.isArray(item.regNo) ? item.regNo.join(', ') : item.regNo;
                    const regNoInfoItem = addIpInfoItem(ipItemInfo, 'Reg #:', regNo || 'N/A');
                    makeInfoItemValueLinkable(regNoInfoItem, caseData.plaintiff_id, 'Reg #:', item.regNo, item.fullFilename);
                }
                ipItem.appendChild(ipItemInfo);
                ipItems.appendChild(ipItem);
            });
            ipSection.appendChild(ipItems);
            caseCard.appendChild(ipSection);
        }
    });

    return caseCard;
}

export function showUpdateCaseOverlay(caseId) {
    const overlay = document.createElement('div');
    overlay.className = 'overlay show';
    const content = document.createElement('div');
    content.className = 'overlay-content';
    const header = document.createElement('div');
    header.className = 'overlay-header';
    const title = document.createElement('div');
    title.className = 'overlay-title';
    title.textContent = 'Reprocess Case';
    const closeBtn = document.createElement('button');
    closeBtn.className = 'close-overlay';
    closeBtn.innerHTML = '&times;';
    closeBtn.addEventListener('click', () => document.body.removeChild(overlay));
    header.appendChild(title);
    header.appendChild(closeBtn);

    const body = document.createElement('div');
    body.className = 'overlay-body';
    const optionsGroup = document.createElement('div');
    optionsGroup.className = 'option-group';
    const optionsTitle = document.createElement('div');
    optionsTitle.className = 'option-group-title';
    optionsTitle.textContent = 'Processing Options:';
    optionsGroup.appendChild(optionsTitle);

    const processingOptions = [
        { name: 'update_steps', label: 'Update Steps', checked: true },
        { name: 'process_pictures', label: 'Process Pictures', checked: true },
        { name: 'upload_files_nas', label: 'Upload Files to NAS', checked: true },
        { name: 'upload_files_cos', label: 'Upload Files to COS', checked: true },
        { name: 'run_plaintiff_overview', label: 'Run Plaintiff Overview', checked: true },
        { name: 'run_summary_translation', label: 'Run Summary Translation', checked: true },
        { name: 'run_step_translation', label: 'Run Step Translation', checked: true },
        { name: 'save_to_db', label: 'Save to Database', checked: true }
    ];
    processingOptions.forEach(option => {
        const checkboxOption = document.createElement('div');
        checkboxOption.className = 'checkbox-option';
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.name = option.name;
        checkbox.id = option.name;
        checkbox.checked = option.checked;
        const label = document.createElement('label');
        label.htmlFor = option.name;
        label.className = 'option-label';
        label.textContent = option.label;
        checkboxOption.appendChild(checkbox);
        checkboxOption.appendChild(label);
        optionsGroup.appendChild(checkboxOption);
    });

    const modeGroup = document.createElement('div');
    modeGroup.className = 'option-group';
    const modeTitle = document.createElement('div');
    modeTitle.className = 'option-group-title';
    modeTitle.textContent = 'Processing Mode:';
    modeGroup.appendChild(modeTitle);
    const modeSelect = document.createElement('select');
    modeSelect.id = 'processing_mode';
    modeSelect.name = 'processing_mode';
    const fullReprocessOption = document.createElement('option');
    fullReprocessOption.value = 'full_reprocess';
    fullReprocessOption.textContent = 'Full Reprocess - Reprocess all steps and clear previous state';
    fullReprocessOption.selected = true;
    const resumeOption = document.createElement('option');
    resumeOption.value = 'resume';
    resumeOption.textContent = 'Resume - Load saved state and only process steps not previously completed';
    modeSelect.appendChild(fullReprocessOption);
    modeSelect.appendChild(resumeOption);
    const modeDescription = document.createElement('div');
    modeDescription.className = 'option-description';
    modeDescription.textContent = 'Resume will process steps not yet processed until all IP is found, while full_reprocess will reprocess the steps already processed and then process steps not yet processed';
    modeGroup.appendChild(modeSelect);
    modeGroup.appendChild(modeDescription);

    const thresholdGroup = document.createElement('div');
    thresholdGroup.className = 'option-group';
    const thresholdTitle = document.createElement('div');
    thresholdTitle.className = 'option-group-title';
    thresholdTitle.textContent = 'Refresh Days Threshold:';
    thresholdGroup.appendChild(thresholdTitle);
    const thresholdInput = document.createElement('input');
    thresholdInput.type = 'number';
    thresholdInput.id = 'refresh_days_threshold';
    thresholdInput.name = 'refresh_days_threshold';
    thresholdInput.value = '15';
    thresholdInput.min = '1';
    thresholdInput.max = '365';
    const thresholdDescription = document.createElement('div');
    thresholdDescription.className = 'option-description';
    thresholdDescription.textContent = 'Number of days to use as threshold for refresh operations';
    thresholdGroup.appendChild(thresholdInput);
    thresholdGroup.appendChild(thresholdDescription);

    body.appendChild(optionsGroup);
    body.appendChild(modeGroup);
    body.appendChild(thresholdGroup);

    const footer = document.createElement('div');
    footer.className = 'overlay-footer';
    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'btn secondary';
    cancelBtn.textContent = 'Cancel';
    cancelBtn.addEventListener('click', () => document.body.removeChild(overlay));
    const proceedBtn = document.createElement('button');
    proceedBtn.className = 'btn primary';
    proceedBtn.textContent = 'Proceed';
    proceedBtn.addEventListener('click', function() {
        const processingOptions = {};
        processingOptions.update_steps = document.getElementById('update_steps').checked;
        processingOptions.process_pictures = document.getElementById('process_pictures').checked;
        processingOptions.upload_files_nas = document.getElementById('upload_files_nas').checked;
        processingOptions.upload_files_cos = document.getElementById('upload_files_cos').checked;
        processingOptions.run_plaintiff_overview = document.getElementById('run_plaintiff_overview').checked;
        processingOptions.run_summary_translation = document.getElementById('run_summary_translation').checked;
        processingOptions.run_step_translation = document.getElementById('run_step_translation').checked;
        processingOptions.save_to_db = document.getElementById('save_to_db').checked;
        processingOptions.processing_mode = document.getElementById('processing_mode').value;
        processingOptions.refresh_days_threshold = parseInt(document.getElementById('refresh_days_threshold').value);
        document.body.removeChild(overlay);
        const caseCard = document.querySelector(`.case-card[data-case-id="${caseId}"]`);
        const { logContentElement, logTitleElement } = createOrGetLogSection(caseCard, 'Reprocess Progress', caseId);
        logContentElement.textContent = 'Starting reprocess...\n';
        updateCaseWithOptions(caseId, processingOptions, logContentElement, logTitleElement);
    });
    footer.appendChild(cancelBtn);
    footer.appendChild(proceedBtn);

    content.appendChild(header);
    content.appendChild(body);
    content.appendChild(footer);
    overlay.appendChild(content);
    document.body.appendChild(overlay);
}

export function updatePagination(total) {
    totalResultsEl.textContent = `${total} results`;
    pageInfoEl.textContent = `Page ${currentPage} of ${totalPages || 1}`;
    prevPageBtn.disabled = currentPage <= 1;
    nextPageBtn.disabled = currentPage >= totalPages || totalPages === 0;
}

export function updateCaseTypeDropdownHeader() {
    const selectedTypes = Array.from(caseTypeCheckboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);
    const headerText = selectedTypes.length > 0 ? selectedTypes.join(', ') : 'Select types';
    const headerSpan = caseTypeDropdownHeader.querySelector('span');
    headerSpan.textContent = headerText;
}

export function showLoading() {
    casesContainer.innerHTML = '<div class="loading">Loading cases...</div>';
}

export function showError(message) {
    casesContainer.innerHTML = `<div class="error">${message}</div>`;
}

export function updateCaseCard(caseId, updatedCase) {
    const existingCard = document.querySelector(`.case-card[data-case-id="${caseId}"]`);
    if (existingCard) {
        const newCard = renderSingleCaseCard(updatedCase);
        existingCard.replaceWith(newCard);
    }
}

export function updateValidationIcons(caseId, status) {
    const validationIcons = document.querySelectorAll(`.validation-icon[data-case-id="${caseId}"]`);
    validationIcons.forEach(icon => {
        icon.classList.remove('active', 'validated', 'review', 'failed');
        if (icon.dataset.status === status) {
            icon.classList.add('active');
            if (status === 'validated') icon.classList.add('validated');
            else if (status === 'review_required') icon.classList.add('review');
            else if (status === 'failed') icon.classList.add('failed');
        }
    });
}

export function displayCaseSteps(caseCard, caseId, steps, stepNumbersCount) {
    const caseDescription = caseCard.querySelector('.case-description');
    const stepsSection = document.createElement('div');
    stepsSection.className = 'log-section';
    stepsSection.id = `steps-section-${caseId}`;

    const stepsSectionHeader = document.createElement('div');
    stepsSectionHeader.className = 'log-section-header';
    const stepsSectionTitle = document.createElement('div');
    stepsSectionTitle.className = 'log-section-title';
    stepsSectionTitle.textContent = 'Case Steps';
    stepsSectionHeader.appendChild(stepsSectionTitle);
    const closeButton = document.createElement('button');
    closeButton.className = 'close-log';
    closeButton.innerHTML = '<i class="fas fa-times"></i>';
    closeButton.title = 'Close Steps';
    closeButton.addEventListener('click', () => stepsSection.style.display = 'none');
    stepsSectionHeader.appendChild(closeButton);
    stepsSection.appendChild(stepsSectionHeader);

    const stepsTable = document.createElement('table');
    stepsTable.className = 'steps-table';
    const tableHeader = document.createElement('thead');
    const headerRow = document.createElement('tr');
    const headers = ['Date Filed', 'Step #', 'Proceeding Text', 'Chinese', 'Files Downloaded', 'Files Failed', 'Last Updated'];
    headers.forEach(headerText => {
        const th = document.createElement('th');
        th.textContent = headerText;
        headerRow.appendChild(th);
    });
    tableHeader.appendChild(headerRow);
    stepsTable.appendChild(tableHeader);

    const tableBody = document.createElement('tbody');
    steps.forEach(step => {
        const row = document.createElement('tr');
        const dateCell = document.createElement('td');
        if (step.step_date_filed) {
            try {
                const date = new Date(step.step_date_filed);
                if (!isNaN(date.getTime())) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    dateCell.textContent = `${year}-${month}-${day}`;
                } else {
                    dateCell.textContent = step.step_date_filed;
                }
            } catch (e) {
                dateCell.textContent = step.step_date_filed;
            }
        } else {
            dateCell.textContent = '-';
        }
        row.appendChild(dateCell);

        const stepNbCell = document.createElement('td');
        if (step.step_nb !== null) {
            const stepNumber = Number(step.step_nb);
            const stepText = isNaN(stepNumber) ? step.step_nb : stepNumber.toString();
            stepNbCell.textContent = stepText;
            if (stepNumbersCount[stepText] > 1) {
                stepNbCell.style.color = '#a00';
            }
        } else {
            stepNbCell.textContent = '-';
        }
        row.appendChild(stepNbCell);

        const proceedingCell = document.createElement('td');
        proceedingCell.textContent = step.proceeding_text || '-';
        row.appendChild(proceedingCell);
        const chineseCell = document.createElement('td');
        chineseCell.textContent = step.proceeding_text_cn || '-';
        row.appendChild(chineseCell);
        const filesDownloadedCell = document.createElement('td');
        filesDownloadedCell.textContent = (step.files_downloaded !== null) ? step.files_downloaded : '-';
        row.appendChild(filesDownloadedCell);
        const filesFailedCell = document.createElement('td');
        filesFailedCell.textContent = (step.files_failed !== null) ? step.files_failed : '-';
        row.appendChild(filesFailedCell);
        const updateTimeCell = document.createElement('td');
        if (step.update_time) {
            try {
                const date = new Date(step.update_time);
                if (!isNaN(date.getTime())) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    updateTimeCell.textContent = `${year}-${month}-${day}`;
                } else {
                    updateTimeCell.textContent = step.update_time;
                }
            } catch (e) {
                updateTimeCell.textContent = step.update_time;
            }
        } else {
            updateTimeCell.textContent = '-';
        }
        row.appendChild(updateTimeCell);
        tableBody.appendChild(row);
    });
    stepsTable.appendChild(tableBody);
    stepsSection.appendChild(stepsTable);

    if (caseDescription) {
        caseCard.insertBefore(stepsSection, caseDescription);
    } else {
        caseCard.appendChild(stepsSection);
    }
}

export function updateStepsCount(stepsInfoItem, count, hasDuplicates) {
    const stepsCount = stepsInfoItem.querySelector('.steps-count');
    stepsInfoItem.querySelector('.steps-loading').style.display = 'none';
    stepsCount.textContent = count;
    if (hasDuplicates) {
        stepsCount.classList.add('duplicate');
    } else {
        stepsCount.classList.remove('duplicate');
    }
}
